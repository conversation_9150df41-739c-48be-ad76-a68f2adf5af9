/**
 * 💾 نظام إدارة النسخ الاحتياطي المتقدم
 * نسخ احتياطي تلقائي ويدوي مع ضغط وتشفير
 */

const fs = require('fs-extra');
const path = require('path');
const { EventEmitter } = require('events');
const { config } = require('./config');
const archiver = require('archiver');
const unzipper = require('unzipper');

class BackupManager extends EventEmitter {
    constructor() {
        super();
        this.backupDir = './backups/';
        this.tempDir = './temp/';
        this.autoBackupInterval = null;
        this.isBackupInProgress = false;
        this.backupHistory = [];
        this.initialized = false;

        // إعدادات النسخ الاحتياطي من الكونفيج
        this.backupConfig = config.database.backup;
        this.maxBackups = 5; // الاحتفاظ بـ 5 نسخ فقط
        this.autoBackupEnabled = this.backupConfig.autoBackup;
        this.autoBackupIntervalMs = this.backupConfig.interval || 300000; // 5 دقائق
        this.compressionEnabled = true; // تفعيل الضغط دائماً
        this.encryptionEnabled = this.backupConfig.encryption;
        this.autoBackupTimer = null;
    }

    // تهيئة نظام النسخ الاحتياطي
    async init() {
        if (this.initialized) return this;

        try {
            console.log('💾 تهيئة نظام النسخ الاحتياطي...');

            // إنشاء المجلدات المطلوبة
            await fs.ensureDir(this.backupDir);
            await fs.ensureDir(this.tempDir);

            // تحميل تاريخ النسخ الاحتياطي
            await this.loadBackupHistory();

            // تنظيف النسخ القديمة
            await this.cleanupOldBackups();

            // تنظيف الملفات المؤقتة القديمة
            await this.cleanupTempFiles();

            // تنظيف دوري للملفات المؤقتة كل ساعة
            setInterval(() => {
                this.cleanupTempFiles();
            }, 60 * 60 * 1000); // كل ساعة

            this.initialized = true;
            console.log('✅ تم تهيئة نظام النسخ الاحتياطي بنجاح');

            return this;
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام النسخ الاحتياطي:', error);
            throw error;
        }
    }





    // إنشاء نسخة احتياطية مضغوطة
    async createBackup(type = 'manual', description = '') {
        if (this.isBackupInProgress) {
            console.log('⚠️ نسخة احتياطية قيد التنفيذ بالفعل');
            return null;
        }

        this.isBackupInProgress = true;
        const startTime = new Date();
        const backupId = this.generateBackupId();

        try {
            console.log(`💾 بدء إنشاء نسخة احتياطية مضغوطة: ${backupId}`);

            // إنشاء معلومات النسخة الاحتياطية
            const backupInfo = {
                id: backupId,
                type: type,
                description: description,
                timestamp: startTime.toISOString(),
                date: startTime.toLocaleDateString('ar-EG'),
                time: startTime.toLocaleTimeString('ar-EG'),
                files: [],
                size: 0,
                compressed: true,
                encrypted: this.encryptionEnabled,
                status: 'in_progress',
                systemInfo: {
                    nodeVersion: process.version,
                    platform: process.platform,
                    arch: process.arch,
                    workingDirectory: process.cwd()
                }
            };

            // إنشاء مجلد مؤقت للنسخة
            const tempBackupDir = path.join(this.tempDir, backupId);
            await fs.ensureDir(tempBackupDir);

            // نسخ قاعدة البيانات
            const dbBackupPath = await this.backupDatabase(backupId, tempBackupDir);
            if (dbBackupPath) {
                const dbStats = await fs.stat(dbBackupPath);
                backupInfo.files.push({
                    name: 'database.db',
                    path: dbBackupPath,
                    size: dbStats.size,
                    type: 'database'
                });
                backupInfo.size += dbStats.size;
            }

            // نسخ ملفات الإعدادات
            const configBackupPath = await this.backupConfigs(backupId, tempBackupDir);
            if (configBackupPath) {
                const configStats = await fs.stat(configBackupPath);
                backupInfo.files.push({
                    name: 'config.json',
                    path: configBackupPath,
                    size: configStats.size,
                    type: 'config'
                });
                backupInfo.size += configStats.size;
            }

            // نسخ تاريخ النسخ الاحتياطية
            const historyBackupPath = await this.backupHistoryFile(backupId, tempBackupDir);
            if (historyBackupPath) {
                const historyStats = await fs.stat(historyBackupPath);
                backupInfo.files.push({
                    name: 'backup_history.json',
                    path: historyBackupPath,
                    size: historyStats.size,
                    type: 'history'
                });
                backupInfo.size += historyStats.size;
            }

            // إنشاء ملف معلومات النسخة
            const infoPath = path.join(tempBackupDir, 'backup_info.json');
            await fs.writeJSON(infoPath, backupInfo, { spaces: 2 });

            // ضغط النسخة الاحتياطية
            console.log(`🔄 بدء ضغط النسخة الاحتياطية: ${backupId}`);
            const zipPath = await this.compressBackup(backupId, tempBackupDir);
            console.log(`📦 مسار الملف المضغوط: ${zipPath}`);

            if (!zipPath) {
                throw new Error('فشل في إنشاء الملف المضغوط');
            }

            // تنظيف المجلد المؤقت
            await fs.remove(tempBackupDir);

            // تحديث معلومات النسخة
            const zipStats = await fs.stat(zipPath);
            backupInfo.compressedSize = zipStats.size;
            backupInfo.filename = path.basename(zipPath);
            backupInfo.filepath = zipPath;
            backupInfo.status = 'completed';
            backupInfo.duration = new Date() - startTime;

            // إضافة النسخة لتاريخ النسخ
            this.backupHistory.unshift(backupInfo);
            await this.saveBackupHistory();

            // تنظيف النسخ القديمة (الاحتفاظ بـ 5 نسخ فقط)
            await this.cleanupOldBackups();

            console.log(`✅ تم إنشاء النسخة الاحتياطية بنجاح: ${backupInfo.filename}`);
            console.log(`📊 الحجم الأصلي: ${Math.round(backupInfo.size / 1024)} KB`);
            console.log(`📦 الحجم المضغوط: ${Math.round(backupInfo.compressedSize / 1024)} KB`);
            console.log(`⏱️ المدة: ${backupInfo.duration}ms`);

            this.emit('backupCreated', backupInfo);
            return backupInfo;

        } catch (error) {
            console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
            this.emit('backupError', { backupId, error: error.message });
            return null;
        } finally {
            this.isBackupInProgress = false;
        }
    }

    // ضغط النسخة الاحتياطية
    async compressBackup(backupId, sourceDir) {
        try {
            console.log(`📦 بدء ضغط المجلد: ${sourceDir}`);
            console.log(`📦 backupDir: ${this.backupDir}`);
            console.log(`📦 backupId: ${backupId}`);

            const zipPath = path.join(this.backupDir, `${backupId}.zip`);
            console.log(`📦 مسار الملف المضغوط: ${zipPath}`);

            return new Promise((resolve, reject) => {
                const output = fs.createWriteStream(zipPath);
                const archive = archiver('zip', {
                    zlib: { level: 9 } // أقصى ضغط
                });

                output.on('close', () => {
                    console.log(`📦 تم ضغط النسخة الاحتياطية: ${archive.pointer()} bytes`);
                    console.log(`📦 مسار الملف النهائي: ${zipPath}`);
                    resolve(zipPath);
                });

                output.on('error', (err) => {
                    console.error('❌ خطأ في كتابة الملف المضغوط:', err);
                    reject(err);
                });

                archive.on('error', (err) => {
                    console.error('❌ خطأ في ضغط النسخة الاحتياطية:', err);
                    reject(err);
                });

                archive.on('warning', (err) => {
                    if (err.code === 'ENOENT') {
                        console.warn('⚠️ تحذير في الضغط:', err);
                    } else {
                        reject(err);
                    }
                });

                archive.pipe(output);
                archive.directory(sourceDir, false);
                archive.finalize();
            });
        } catch (error) {
            console.error('❌ خطأ في إعداد الضغط:', error);
            throw error;
        }
    }

    // استعادة النسخة الاحتياطية (الآن يستخدم الاستعادة المباشرة افتراضياً)
    async restoreBackup(zipFilePath, databaseManager = null, chatId = null) {
        // استخدام الاستعادة المباشرة كافتراضي
        if (databaseManager) {
            return await this.restoreBackupLive(zipFilePath, databaseManager, chatId);
        }

        // النظام القديم (للتوافق مع الإصدارات السابقة)
        return await this.restoreBackupLegacy(zipFilePath, chatId);
    }

    // النظام القديم للاستعادة مع إعادة التشغيل (للتوافق فقط)
    async restoreBackupLegacy(zipFilePath, chatId = null) {
        if (this.isBackupInProgress) {
            throw new Error('عملية نسخ احتياطي قيد التنفيذ');
        }

        this.isBackupInProgress = true;
        const restoreId = this.generateBackupId();

        try {
            console.log(`🔄 بدء استعادة النسخة الاحتياطية (النظام القديم): ${restoreId}`);

            // إنشاء مجلد مؤقت للاستعادة
            const tempRestoreDir = path.join(this.tempDir, `restore_${restoreId}`);
            await fs.ensureDir(tempRestoreDir);

            // استخراج الملفات
            await this.extractBackup(zipFilePath, tempRestoreDir);

            // قراءة معلومات النسخة
            const infoPath = path.join(tempRestoreDir, 'backup_info.json');
            let backupInfo = {};
            if (await fs.pathExists(infoPath)) {
                backupInfo = await fs.readJSON(infoPath);
            }

            // استعادة قاعدة البيانات
            const dbPath = path.join(tempRestoreDir, 'database.db');
            if (await fs.pathExists(dbPath)) {
                await this.restoreDatabase(dbPath);
                console.log('✅ تم استعادة قاعدة البيانات');
            }

            // استعادة الإعدادات
            const configPath = path.join(tempRestoreDir, 'config.json');
            if (await fs.pathExists(configPath)) {
                await this.restoreConfigs(configPath);
                console.log('✅ تم استعادة الإعدادات');
            }

            // تنظيف المجلد المؤقت
            await fs.remove(tempRestoreDir);

            console.log('✅ تم استعادة النسخة الاحتياطية بنجاح');
            console.log('🔄 سيتم إعادة تهيئة النظام تلقائياً...');

            // إشعار النظام بانتهاء الاستعادة
            this.emit('backupRestored', {
                restoreId,
                backupInfo,
                chatId: chatId,
                timestamp: new Date().toISOString(),
                success: true
            });

            return { success: true, restoreId, backupInfo };

        } catch (error) {
            console.error('❌ خطأ في استعادة النسخة الاحتياطية:', error);
            this.emit('restoreError', { restoreId, error: error.message });
            throw error;
        } finally {
            this.isBackupInProgress = false;
        }
    }

    // استعادة النسخة الاحتياطية بدون إعادة تشغيل (محسن لـ Railway.com)
    async restoreBackupLive(zipFilePath, databaseManager, chatId = null) {
        if (this.isBackupInProgress) {
            throw new Error('عملية نسخ احتياطي قيد التنفيذ');
        }

        this.isBackupInProgress = true;
        const restoreId = this.generateBackupId();

        try {
            console.log(`🔄 بدء الاستعادة المباشرة للنسخة الاحتياطية: ${restoreId}`);

            // إنشاء مجلد مؤقت للاستعادة
            const tempRestoreDir = path.join(this.tempDir, `restore_live_${restoreId}`);
            await fs.ensureDir(tempRestoreDir);

            // استخراج الملفات
            await this.extractBackup(zipFilePath, tempRestoreDir);

            // قراءة معلومات النسخة
            const infoPath = path.join(tempRestoreDir, 'backup_info.json');
            let backupInfo = {};
            if (await fs.pathExists(infoPath)) {
                backupInfo = await fs.readJSON(infoPath);
            }

            // استعادة قاعدة البيانات بدون إعادة تشغيل
            const dbPath = path.join(tempRestoreDir, 'database.db');
            if (await fs.pathExists(dbPath)) {
                await this.restoreDatabaseLive(dbPath, databaseManager);
                console.log('✅ تم استعادة قاعدة البيانات بدون إعادة تشغيل');
            }

            // استعادة الإعدادات
            const configPath = path.join(tempRestoreDir, 'config.json');
            if (await fs.pathExists(configPath)) {
                await this.restoreConfigs(configPath);
                console.log('✅ تم استعادة الإعدادات');
            }

            // تنظيف المجلد المؤقت
            await fs.remove(tempRestoreDir);

            console.log('✅ تم استعادة النسخة الاحتياطية بنجاح بدون إعادة تشغيل');

            // إشعار النظام بانتهاء الاستعادة المباشرة
            this.emit('backupRestoredLive', {
                restoreId,
                backupInfo,
                chatId: chatId,
                timestamp: new Date().toISOString(),
                success: true
            });

            return { success: true, restoreId, backupInfo };

        } catch (error) {
            console.error('❌ خطأ في الاستعادة المباشرة للنسخة الاحتياطية:', error);
            this.emit('restoreError', { restoreId, error: error.message });
            throw error;
        } finally {
            this.isBackupInProgress = false;
            // تنظيف المجلد المؤقت في حالة الخطأ
            try {
                const tempRestoreDir = path.join(this.tempDir, `restore_live_${restoreId}`);
                if (await fs.pathExists(tempRestoreDir)) {
                    await fs.remove(tempRestoreDir);
                }
            } catch (cleanupError) {
                console.error('❌ خطأ في تنظيف المجلد المؤقت:', cleanupError);
            }
        }
    }

    // استخراج النسخة الاحتياطية
    async extractBackup(zipFilePath, targetDir) {
        return new Promise((resolve, reject) => {
            fs.createReadStream(zipFilePath)
                .pipe(unzipper.Extract({ path: targetDir }))
                .on('close', () => {
                    console.log('📂 تم استخراج النسخة الاحتياطية');
                    resolve();
                })
                .on('error', (err) => {
                    console.error('❌ خطأ في استخراج النسخة الاحتياطية:', err);
                    reject(err);
                });
        });
    }

    // بدء النسخ التلقائي
    startAutoBackup(intervalMinutes = 5) {
        this.stopAutoBackup(); // إيقاف أي نسخ تلقائي سابق

        const intervalMs = intervalMinutes * 60 * 1000;
        this.autoBackupIntervalMs = intervalMs;

        console.log(`🔄 بدء النسخ التلقائي كل ${intervalMinutes} دقيقة`);

        this.autoBackupTimer = setInterval(async () => {
            try {
                console.log('⏰ تنفيذ النسخ التلقائي...');
                const backup = await this.createBackup('auto', `نسخة تلقائية - ${new Date().toLocaleString('ar-EG')}`);
                if (backup) {
                    console.log('📤 إرسال إشعار النسخة التلقائية...');
                    this.emit('autoBackupCompleted', backup);
                } else {
                    console.error('❌ فشل إنشاء النسخة التلقائية');
                }
            } catch (error) {
                console.error('❌ خطأ في النسخ التلقائي:', error);
                this.emit('autoBackupError', error);
            }
        }, intervalMs);

        this.autoBackupEnabled = true;
        return true;
    }

    // إيقاف النسخ التلقائي
    stopAutoBackup() {
        if (this.autoBackupTimer) {
            clearInterval(this.autoBackupTimer);
            this.autoBackupTimer = null;
            console.log('⏹️ تم إيقاف النسخ التلقائي');
        }
        this.autoBackupEnabled = false;
        return true;
    }

    // فحص حالة النسخ التلقائي
    isAutoBackupEnabled() {
        return this.autoBackupEnabled && this.autoBackupTimer !== null;
    }

    // الحصول على تاريخ النسخ الاحتياطية
    async getBackupHistory() {
        // التأكد من أن النسخ المعروضة موجودة فعلياً
        const validBackups = [];

        for (const backup of this.backupHistory || []) {
            const filePath = path.join(this.backupDir, backup.filename);
            if (await fs.pathExists(filePath)) {
                validBackups.push(backup);
            } else {
                console.log(`⚠️ النسخة ${backup.filename} غير موجودة فعلياً، سيتم إزالتها من التاريخ`);
            }
        }

        // تحديث التاريخ إذا تم العثور على نسخ غير موجودة
        if (validBackups.length !== this.backupHistory.length) {
            this.backupHistory = validBackups;
            await this.saveBackupHistory();
        }

        return validBackups;
    }



    // نسخ قاعدة البيانات
    async backupDatabase(backupId, targetDir = this.backupDir) {
        try {
            const dbPath = './minecraft_bot.db';
            if (await fs.pathExists(dbPath)) {
                const backupPath = path.join(targetDir, 'database.db');
                await fs.copy(dbPath, backupPath);
                console.log(`📊 تم نسخ قاعدة البيانات: ${backupPath}`);
                return backupPath;
            }
            return null;
        } catch (error) {
            console.error('❌ خطأ في نسخ قاعدة البيانات:', error);
            return null;
        }
    }

    // استعادة قاعدة البيانات (محسن لـ Railway.com)
    async restoreDatabase(backupDbPath) {
        try {
            const dbPath = './minecraft_bot.db';

            // إشعار النظام بأن الاستعادة ستبدأ
            console.log('🔄 بدء استعادة قاعدة البيانات...');
            this.emit('databaseRestoreStarted');

            // انتظار قليل للسماح للنظام بإغلاق الاتصالات
            await new Promise(resolve => setTimeout(resolve, 2000));

            // محاولة نسخ قاعدة البيانات مع إعادة المحاولة
            let attempts = 0;
            const maxAttempts = 5;

            while (attempts < maxAttempts) {
                try {
                    // إنشاء نسخة احتياطية من قاعدة البيانات الحالية
                    const backupCurrentDb = `${dbPath}.backup_${Date.now()}`;
                    if (await fs.pathExists(dbPath)) {
                        await fs.copy(dbPath, backupCurrentDb);
                    }

                    // محاولة استبدال قاعدة البيانات
                    await fs.copy(backupDbPath, dbPath);

                    // حذف النسخة الاحتياطية المؤقتة
                    if (await fs.pathExists(backupCurrentDb)) {
                        await fs.remove(backupCurrentDb);
                    }

                    console.log(`📊 تم استعادة قاعدة البيانات من: ${backupDbPath}`);
                    return true;

                } catch (error) {
                    attempts++;
                    if (error.code === 'EBUSY' && attempts < maxAttempts) {
                        console.log(`⏳ قاعدة البيانات مشغولة، محاولة ${attempts}/${maxAttempts}...`);
                        await new Promise(resolve => setTimeout(resolve, 3000));
                        continue;
                    }
                    throw error;
                }
            }

            throw new Error('فشل في استعادة قاعدة البيانات بعد عدة محاولات');

        } catch (error) {
            console.error('❌ خطأ في استعادة قاعدة البيانات:', error);
            throw error;
        }
    }

    // استعادة قاعدة البيانات بدون إعادة تشغيل (محسن لـ Railway.com)
    async restoreDatabaseLive(backupDbPath, databaseManager = null) {
        try {
            console.log('🔄 بدء الاستعادة المباشرة لقاعدة البيانات...');

            if (!databaseManager) {
                throw new Error('مدير قاعدة البيانات مطلوب للاستعادة المباشرة');
            }

            // إغلاق الاتصال الحالي
            console.log('🔌 إغلاق اتصال قاعدة البيانات الحالي...');
            await databaseManager.close();

            // انتظار للتأكد من إغلاق الاتصال
            await new Promise(resolve => setTimeout(resolve, 1000));

            // نسخ قاعدة البيانات المستعادة
            const dbPath = './minecraft_bot.db';
            console.log('📁 استبدال ملف قاعدة البيانات...');

            // إنشاء نسخة احتياطية من قاعدة البيانات الحالية
            const backupCurrentDb = `${dbPath}.backup_${Date.now()}`;
            if (await fs.pathExists(dbPath)) {
                await fs.copy(dbPath, backupCurrentDb);
                console.log(`💾 تم إنشاء نسخة احتياطية: ${backupCurrentDb}`);
            }

            // استبدال قاعدة البيانات
            await fs.copy(backupDbPath, dbPath);
            console.log('✅ تم استبدال ملف قاعدة البيانات');

            // إعادة تهيئة قاعدة البيانات
            console.log('🔄 إعادة تهيئة اتصال قاعدة البيانات...');
            await databaseManager.init();

            // إشعار بإعادة تهيئة قاعدة البيانات
            this.emit('databaseReinitialized', {
                timestamp: new Date().toISOString(),
                success: true
            });

            // حذف النسخة الاحتياطية المؤقتة
            if (await fs.pathExists(backupCurrentDb)) {
                await fs.remove(backupCurrentDb);
                console.log('🗑️ تم حذف النسخة الاحتياطية المؤقتة');
            }

            console.log('✅ تم استعادة قاعدة البيانات بنجاح بدون إعادة تشغيل');
            return true;

        } catch (error) {
            console.error('❌ خطأ في الاستعادة المباشرة لقاعدة البيانات:', error);
            throw error;
        }
    }

    // نسخ ملفات الإعدادات
    async backupConfigs(backupId, targetDir = this.backupDir) {
        try {
            const configData = {
                env: {},
                config: {},
                package: {}
            };

            // نسخ متغيرات البيئة (بدون كلمات المرور)
            if (await fs.pathExists('.env')) {
                const envContent = await fs.readFile('.env', 'utf8');
                const envLines = envContent.split('\n');
                for (const line of envLines) {
                    if (line.includes('=') && !line.includes('PASSWORD') && !line.includes('TOKEN')) {
                        const [key, value] = line.split('=');
                        configData.env[key] = value;
                    }
                }
            }

            // نسخ إعدادات النظام
            if (await fs.pathExists('config.js')) {
                // نسخ الإعدادات الأساسية فقط (بدون كلمات المرور)
                configData.config = {
                    supportedVersions: config.supportedVersions || {},
                    database: {
                        type: 'sqlite' // نوع قاعدة البيانات فقط
                    }
                };
            }

            // نسخ معلومات الحزمة
            if (await fs.pathExists('package.json')) {
                const packageData = await fs.readJSON('package.json');
                configData.package = {
                    name: packageData.name,
                    version: packageData.version,
                    dependencies: packageData.dependencies
                };
            }

            const backupPath = path.join(targetDir, 'config.json');
            await fs.writeJSON(backupPath, configData, { spaces: 2 });
            console.log(`⚙️ تم نسخ ملفات الإعدادات: ${backupPath}`);
            return backupPath;

        } catch (error) {
            console.error('❌ خطأ في نسخ ملفات الإعدادات:', error);
            return null;
        }
    }

    // استعادة الإعدادات
    async restoreConfigs(backupConfigPath) {
        try {
            const configData = await fs.readJSON(backupConfigPath);
            console.log(`⚙️ تم استعادة الإعدادات من: ${backupConfigPath}`);
            // ملاحظة: في التطبيق الحقيقي، يجب إعادة تشغيل النظام لتطبيق الإعدادات
            return true;
        } catch (error) {
            console.error('❌ خطأ في استعادة الإعدادات:', error);
            throw error;
        }
    }

    // نسخ تاريخ النسخ الاحتياطية
    async backupHistoryFile(backupId, targetDir = this.backupDir) {
        try {
            // التحقق من وجود ملف التاريخ
            if (!this.historyFile || !await fs.pathExists(this.historyFile)) {
                console.log('📚 ملف تاريخ النسخ غير موجود، سيتم إنشاء ملف فارغ');
                const historyPath = path.join(targetDir, 'backup_history.json');
                await fs.writeJSON(historyPath, [], { spaces: 2 });
                return historyPath;
            }

            const historyPath = path.join(targetDir, 'backup_history.json');
            await fs.copy(this.historyFile, historyPath);
            console.log(`📚 تم نسخ تاريخ النسخ الاحتياطية: ${historyPath}`);
            return historyPath;
        } catch (error) {
            console.error('❌ خطأ في نسخ تاريخ النسخ الاحتياطية:', error);
            return null;
        }
    }

    // نسخ السجلات
    async backupLogs(backupId) {
        try {
            const logsDir = './logs';
            if (await fs.pathExists(logsDir)) {
                const backupPath = path.join(this.backupDir, `${backupId}_logs`);
                await fs.copy(logsDir, backupPath);
                console.log(`📝 تم نسخ السجلات: ${backupPath}`);
                return backupPath;
            }
            return null;
        } catch (error) {
            console.error('❌ خطأ في نسخ السجلات:', error);
            return null;
        }
    }







    // حذف نسخة احتياطية
    async deleteBackup(backupId) {
        try {
            console.log(`🗑️ حذف النسخة الاحتياطية: ${backupId}`);

            // البحث عن النسخة في التاريخ
            const backupInfo = this.backupHistory.find(backup => backup.id === backupId);

            // إذا لم توجد في التاريخ، ابحث عن الملف مباشرة
            let zipPath;
            if (backupInfo && backupInfo.filename) {
                zipPath = path.join(this.backupDir, backupInfo.filename);
            } else {
                // البحث عن الملف في المجلد
                const files = await fs.readdir(this.backupDir);
                const matchingFile = files.find(file => file.includes(backupId) && file.endsWith('.zip'));
                if (matchingFile) {
                    zipPath = path.join(this.backupDir, matchingFile);
                } else {
                    console.log(`⚠️ النسخة الاحتياطية ${backupId} غير موجودة`);
                    return false;
                }
            }

            // حذف الملف المضغوط
            if (await fs.pathExists(zipPath)) {
                await fs.remove(zipPath);
                console.log(`🗑️ تم حذف الملف المضغوط: ${zipPath}`);
            }

            // حذف ملف المعلومات (إذا كان موجود)
            const metaPath = path.join(this.backupDir, `${backupId}.json`);
            if (await fs.pathExists(metaPath)) {
                await fs.remove(metaPath);
            }

            // إزالة من التاريخ
            this.backupHistory = this.backupHistory.filter(backup => backup.id !== backupId);
            await this.saveBackupHistory();

            console.log(`✅ تم حذف النسخة الاحتياطية: ${backupId}`);
            this.emit('backupDeleted', backupInfo || { id: backupId });
            return true;

        } catch (error) {
            console.error(`❌ خطأ في حذف النسخة الاحتياطية ${backupId}:`, error);
            throw error;
        }
    }

    // حذف جميع النسخ الاحتياطية
    async deleteAllBackups() {
        try {
            console.log('🗑️ بدء حذف جميع النسخ الاحتياطية...');

            // قراءة جميع الملفات في مجلد النسخ
            const files = await fs.readdir(this.backupDir);
            const backupFiles = files.filter(file => file.endsWith('.zip') && file.startsWith('backup_'));

            let deletedCount = 0;

            // حذف كل ملف نسخة احتياطية
            for (const filename of backupFiles) {
                try {
                    const filePath = path.join(this.backupDir, filename);
                    await fs.remove(filePath);
                    console.log(`🗑️ تم حذف: ${filename}`);
                    deletedCount++;
                } catch (error) {
                    console.error(`❌ خطأ في حذف ${filename}:`, error);
                }
            }

            // مسح تاريخ النسخ
            this.backupHistory = [];
            await this.saveBackupHistory();

            console.log(`✅ تم حذف ${deletedCount} نسخة احتياطية`);
            this.emit('allBackupsDeleted', { deletedCount });

            return { success: true, deletedCount };

        } catch (error) {
            console.error('❌ خطأ في حذف جميع النسخ الاحتياطية:', error);
            throw error;
        }
    }

    // تنظيف النسخ القديمة
    async cleanupOldBackups() {
        try {
            if (this.backupHistory.length <= this.maxBackups) {
                return;
            }

            console.log(`🧹 تنظيف النسخ الاحتياطية القديمة (الحد الأقصى: ${this.maxBackups})`);

            // ترتيب النسخ حسب التاريخ (الأحدث أولاً)
            this.backupHistory.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

            // حذف النسخ الزائدة
            const backupsToDelete = this.backupHistory.slice(this.maxBackups);
            let deletedCount = 0;

            for (const backup of backupsToDelete) {
                try {
                    await this.deleteBackup(backup.id);
                    deletedCount++;
                } catch (error) {
                    console.error(`❌ خطأ في حذف النسخة الاحتياطية ${backup.id}:`, error);
                }
            }

            if (deletedCount > 0) {
                console.log(`✅ تم حذف ${deletedCount} نسخة احتياطية قديمة`);
            }

        } catch (error) {
            console.error('❌ خطأ في تنظيف النسخ الاحتياطية:', error);
        }
    }

    // تنظيف الملفات المؤقتة القديمة
    async cleanupTempFiles() {
        try {
            if (!await fs.pathExists(this.tempDir)) {
                return;
            }

            const files = await fs.readdir(this.tempDir);
            const now = Date.now();
            const maxAge = 24 * 60 * 60 * 1000; // 24 ساعة
            let deletedCount = 0;

            for (const file of files) {
                try {
                    const filePath = path.join(this.tempDir, file);
                    const stats = await fs.stat(filePath);

                    // حذف الملفات الأقدم من 24 ساعة
                    if (now - stats.mtime.getTime() > maxAge) {
                        await fs.remove(filePath);
                        deletedCount++;
                        console.log(`🗑️ تم حذف الملف المؤقت: ${file}`);
                    }
                } catch (error) {
                    console.error(`❌ خطأ في حذف الملف المؤقت ${file}:`, error);
                }
            }

            if (deletedCount > 0) {
                console.log(`✅ تم تنظيف ${deletedCount} ملف مؤقت قديم`);
            }

        } catch (error) {
            console.error('❌ خطأ في تنظيف الملفات المؤقتة:', error);
        }
    }

    // تحميل تاريخ النسخ الاحتياطي
    async loadBackupHistory() {
        try {
            const historyPath = path.join(this.backupDir, 'backup_history.json');
            if (await fs.pathExists(historyPath)) {
                this.backupHistory = await fs.readJson(historyPath);
                console.log(`📚 تم تحميل تاريخ النسخ الاحتياطي: ${this.backupHistory.length} نسخة`);
            }

            // التحقق من النسخ الموجودة فعلياً وإعادة بناء التاريخ
            await this.rebuildBackupHistory();
        } catch (error) {
            console.error('❌ خطأ في تحميل تاريخ النسخ الاحتياطي:', error);
            this.backupHistory = [];
            // محاولة إعادة بناء التاريخ من الملفات الموجودة
            await this.rebuildBackupHistory();
        }
    }

    // إعادة بناء تاريخ النسخ من الملفات الموجودة فعلياً
    async rebuildBackupHistory() {
        try {
            const files = await fs.readdir(this.backupDir);
            const backupFiles = files.filter(file => file.endsWith('.zip') && file.startsWith('backup_'));

            // إنشاء قائمة جديدة من النسخ الموجودة فعلياً
            const actualBackups = [];

            for (const filename of backupFiles) {
                const filePath = path.join(this.backupDir, filename);
                const stats = await fs.stat(filePath);

                // استخراج معلومات النسخة من اسم الملف
                const match = filename.match(/backup_(.+)_([a-z0-9]+)\.zip$/);
                if (match) {
                    const [, timestamp, id] = match;
                    const backupInfo = {
                        id: id,
                        filename: filename,
                        timestamp: timestamp,
                        date: new Date(timestamp.replace(/T/, ' ').replace(/-/g, '/').replace(/Z$/, '')),
                        size: stats.size,
                        compressedSize: stats.size,
                        status: 'completed',
                        type: 'manual'
                    };
                    actualBackups.push(backupInfo);
                }
            }

            // ترتيب النسخ حسب التاريخ (الأحدث أولاً)
            actualBackups.sort((a, b) => new Date(b.date) - new Date(a.date));

            // تحديث التاريخ
            this.backupHistory = actualBackups;
            await this.saveBackupHistory();

            console.log(`🔄 تم إعادة بناء تاريخ النسخ: ${actualBackups.length} نسخة موجودة فعلياً`);

        } catch (error) {
            console.error('❌ خطأ في إعادة بناء تاريخ النسخ:', error);
        }
    }

    // حفظ تاريخ النسخ الاحتياطي
    async saveBackupHistory() {
        try {
            const historyPath = path.join(this.backupDir, 'backup_history.json');
            await fs.writeJson(historyPath, this.backupHistory, { spaces: 2 });
        } catch (error) {
            console.error('❌ خطأ في حفظ تاريخ النسخ الاحتياطي:', error);
        }
    }

    // توليد معرف النسخة الاحتياطية
    generateBackupId() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const random = Math.random().toString(36).substring(2, 8);
        return `backup_${timestamp}_${random}`;
    }

    // تنسيق حجم الملف
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // الحصول على قائمة النسخ الاحتياطية
    getBackupList() {
        return this.backupHistory.slice(); // نسخة من المصفوفة
    }

    // الحصول على معلومات النسخة الاحتياطية
    async getBackupInfo(backupId) {
        try {
            const backupMetaPath = path.join(this.backupDir, `${backupId}.json`);
            if (await fs.pathExists(backupMetaPath)) {
                return await fs.readJson(backupMetaPath);
            }
            return null;
        } catch (error) {
            console.error(`❌ خطأ في الحصول على معلومات النسخة الاحتياطية ${backupId}:`, error);
            return null;
        }
    }

    // إغلاق نظام النسخ الاحتياطي
    async close() {
        try {
            console.log('💾 إغلاق نظام النسخ الاحتياطي...');

            // إيقاف النسخ الاحتياطي التلقائي
            this.stopAutoBackup();

            // انتظار انتهاء أي نسخة احتياطية قيد التنفيذ
            while (this.isBackupInProgress) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // حفظ التاريخ
            await this.saveBackupHistory();

            console.log('✅ تم إغلاق نظام النسخ الاحتياطي بنجاح');
        } catch (error) {
            console.error('❌ خطأ في إغلاق نظام النسخ الاحتياطي:', error);
        }
    }
}

module.exports = BackupManager;
