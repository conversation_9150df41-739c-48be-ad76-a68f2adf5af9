/**
 * 🗄️ نظام إدارة قاعدة البيانات المتقدم
 * يدعم SQLite, PostgreSQL, MySQL, MariaDB, MongoDB, Redis وقواعد بيانات أخرى
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs-extra');
const { config } = require('./config');

class DatabaseManager {
    constructor() {
        this.databaseUrl = this.getDatabaseUrl();
        this.dbType = this.detectDatabaseType(this.databaseUrl);
        this.db = null;
        this.client = null;
        this.initialized = false;

        console.log(`🗄️ نوع قاعدة البيانات: ${this.dbType}`);
        console.log(`🔗 رابط قاعدة البيانات: ${this.databaseUrl.replace(/\/\/.*@/, '//***@')}`);
    }

    // الحصول على رابط قاعدة البيانات مع التراجع التلقائي لـ SQLite
    getDatabaseUrl() {
        // إذا كان DATABASE_URL محدد في متغيرات البيئة
        if (process.env.DATABASE_URL) {
            console.log('🔗 استخدام DATABASE_URL من متغيرات البيئة');
            return process.env.DATABASE_URL;
        }

        // إذا كان في بيئة الإنتاج ولا يوجد DATABASE_URL
        if (process.env.NODE_ENV === 'production') {
            console.log('⚠️ بيئة الإنتاج بدون DATABASE_URL، التراجع لـ SQLite');
        }

        // التراجع الافتراضي لـ SQLite
        const defaultSqliteUrl = 'sqlite:./minecraft_bot.db';
        console.log('🗄️ استخدام SQLite كقاعدة بيانات افتراضية');
        return defaultSqliteUrl;
    }

    // اكتشاف نوع قاعدة البيانات من الرابط
    detectDatabaseType(url) {
        if (url.startsWith('postgresql://') || url.startsWith('postgres://')) return 'postgresql';
        if (url.startsWith('mysql://') || url.startsWith('mysql2://')) return 'mysql';
        if (url.startsWith('mariadb://')) return 'mariadb';
        if (url.startsWith('mssql://') || url.startsWith('sqlserver://')) return 'mssql';
        if (url.startsWith('oracle://')) return 'oracle';
        if (url.startsWith('mongodb://') || url.startsWith('mongodb+srv://')) return 'mongodb';
        if (url.startsWith('redis://')) return 'redis';
        return 'sqlite';
    }

    // تهيئة قاعدة البيانات
    async init() {
        if (this.initialized) return this;

        try {
            console.log('🔧 تهيئة قاعدة البيانات...');

            switch (this.dbType) {
                case 'sqlite':
                    await this.initSQLite();
                    break;
                case 'postgresql':
                    await this.initPostgreSQL();
                    break;
                case 'mysql':
                case 'mariadb':
                    await this.initMySQL();
                    break;
                case 'mongodb':
                    await this.initMongoDB();
                    break;
                case 'redis':
                    await this.initRedis();
                    break;
                default:
                    throw new Error(`نوع قاعدة البيانات غير مدعوم: ${this.dbType}`);
            }

            await this.createTables();
            await this.insertDefaultData();

            this.initialized = true;
            console.log('✅ تم تهيئة قاعدة البيانات بنجاح');

            return this;
        } catch (error) {
            console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);

            // إذا فشل الاتصال بـ PostgreSQL، حاول التراجع لـ SQLite
            if (this.dbType === 'postgresql' && process.env.NODE_ENV !== 'production') {
                console.log('🔄 محاولة التراجع لـ SQLite...');
                try {
                    this.databaseUrl = 'sqlite:./minecraft_bot.db';
                    this.dbType = 'sqlite';
                    await this.initSQLite();
                    await this.createTables();
                    await this.insertDefaultData();

                    this.initialized = true;
                    console.log('✅ تم التراجع لـ SQLite بنجاح');
                    return this;
                } catch (fallbackError) {
                    console.error('❌ فشل التراجع لـ SQLite:', fallbackError);
                }
            }

            throw error;
        }
    }

    // تهيئة SQLite
    async initSQLite() {
        const dbPath = this.databaseUrl.replace('sqlite:', '');
        const dbDir = path.dirname(dbPath);
        
        // إنشاء المجلد إذا لم يكن موجوداً
        await fs.ensureDir(dbDir);
        
        this.db = new sqlite3.Database(dbPath, (err) => {
            if (err) {
                console.error('❌ خطأ في الاتصال بـ SQLite:', err);
                throw err;
            }
            console.log('✅ تم الاتصال بـ SQLite بنجاح');
        });

        // تفعيل المفاتيح الخارجية
        this.db.run('PRAGMA foreign_keys = ON');
        this.db.run('PRAGMA journal_mode = WAL');
        this.db.run('PRAGMA synchronous = NORMAL');
        this.db.run('PRAGMA cache_size = 1000');
        this.db.run('PRAGMA temp_store = MEMORY');
    }

    // تهيئة PostgreSQL
    async initPostgreSQL() {
        const { Pool } = require('pg');

        // إعداد الاتصال مع دعم Railway.com
        const connectionConfig = {
            connectionString: this.databaseUrl,
            min: config.database.options.pool.min,
            max: config.database.options.pool.max,
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: config.database.options.timeout,
            // إعدادات إضافية لـ Railway
            ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
            keepAlive: true,
            keepAliveInitialDelayMillis: 10000
        };

        this.client = new Pool(connectionConfig);

        // معالجة أخطاء الاتصال
        this.client.on('error', (err) => {
            console.error('❌ خطأ في PostgreSQL pool:', err);
        });

        // اختبار الاتصال
        const client = await this.client.connect();
        await client.query('SELECT NOW()');
        client.release();
        console.log('✅ تم الاتصال بـ PostgreSQL بنجاح');
    }

    // تهيئة MySQL/MariaDB
    async initMySQL() {
        const mysql = require('mysql2/promise');
        this.client = mysql.createPool({
            uri: this.databaseUrl,
            connectionLimit: config.database.options.pool.max,
            acquireTimeout: config.database.options.timeout,
            timeout: config.database.options.timeout,
            reconnect: true
        });

        // اختبار الاتصال
        const connection = await this.client.getConnection();
        await connection.query('SELECT 1');
        connection.release();
        console.log('✅ تم الاتصال بـ MySQL/MariaDB بنجاح');
    }

    // تهيئة MongoDB
    async initMongoDB() {
        const { MongoClient } = require('mongodb');
        this.client = new MongoClient(this.databaseUrl, {
            maxPoolSize: config.database.options.pool.max,
            minPoolSize: config.database.options.pool.min,
            serverSelectionTimeoutMS: config.database.options.timeout
        });

        await this.client.connect();
        this.db = this.client.db();
        console.log('✅ تم الاتصال بـ MongoDB بنجاح');
    }

    // تهيئة Redis
    async initRedis() {
        const redis = require('redis');
        this.client = redis.createClient({
            url: this.databaseUrl,
            socket: {
                connectTimeout: config.database.options.timeout,
                commandTimeout: config.database.options.timeout
            }
        });

        await this.client.connect();
        console.log('✅ تم الاتصال بـ Redis بنجاح');
    }

    // إنشاء الجداول
    async createTables() {
        console.log('📋 إنشاء الجداول...');
        
        switch (this.dbType) {
            case 'sqlite':
                await this.createSQLiteTables();
                break;
            case 'postgresql':
                await this.createPostgreSQLTables();
                break;
            case 'mysql':
            case 'mariadb':
                await this.createMySQLTables();
                break;
            case 'mongodb':
                await this.createMongoCollections();
                break;
            case 'redis':
                // Redis لا يحتاج إنشاء جداول
                break;
        }
    }

    // إنشاء جداول SQLite
    async createSQLiteTables() {
        return new Promise((resolve, reject) => {
            this.db.serialize(() => {
                // جدول المستخدمين
                this.db.run(`
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        telegram_id INTEGER UNIQUE NOT NULL,
                        username TEXT,
                        first_name TEXT,
                        last_name TEXT,
                        language_code TEXT DEFAULT 'ar',
                        is_admin BOOLEAN DEFAULT 0,
                        is_active BOOLEAN DEFAULT 1,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        last_activity DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                `);

                // جدول البوتات
                this.db.run(`
                    CREATE TABLE IF NOT EXISTS bots (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        bot_name TEXT NOT NULL,
                        server_host TEXT NOT NULL,
                        server_port INTEGER NOT NULL,
                        minecraft_version TEXT NOT NULL,
                        edition TEXT NOT NULL CHECK(edition IN ('java', 'bedrock')),
                        status TEXT DEFAULT 'stopped' CHECK(status IN ('running', 'stopped', 'connecting', 'error', 'created')),
                        auto_reconnect BOOLEAN DEFAULT 1,
                        auto_respawn BOOLEAN DEFAULT 1,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        last_connected DATETIME,
                        connection_count INTEGER DEFAULT 0,
                        FOREIGN KEY (user_id) REFERENCES users (telegram_id) ON DELETE CASCADE,
                        UNIQUE(user_id, bot_name, server_host, server_port)
                    )
                `);

                // جدول إحصائيات البوتات
                this.db.run(`
                    CREATE TABLE IF NOT EXISTS bot_stats (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        bot_id INTEGER NOT NULL,
                        connected_at DATETIME NOT NULL,
                        disconnected_at DATETIME,
                        duration INTEGER,
                        disconnect_reason TEXT,
                        messages_sent INTEGER DEFAULT 0,
                        commands_executed INTEGER DEFAULT 0,
                        errors_count INTEGER DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (bot_id) REFERENCES bots (id) ON DELETE CASCADE
                    )
                `);

                // جدول الإعدادات
                this.db.run(`
                    CREATE TABLE IF NOT EXISTS settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        key TEXT UNIQUE NOT NULL,
                        value TEXT NOT NULL,
                        value_type TEXT DEFAULT 'string' CHECK(value_type IN ('string', 'number', 'boolean', 'json')),
                        description TEXT,
                        category TEXT DEFAULT 'general',
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                `);

                // جدول السجلات
                this.db.run(`
                    CREATE TABLE IF NOT EXISTS logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        level TEXT NOT NULL,
                        message TEXT NOT NULL,
                        data TEXT,
                        user_id INTEGER,
                        bot_id INTEGER,
                        ip_address TEXT,
                        user_agent TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL,
                        FOREIGN KEY (bot_id) REFERENCES bots (id) ON DELETE SET NULL
                    )
                `);

                // جدول النسخ الاحتياطي
                this.db.run(`
                    CREATE TABLE IF NOT EXISTS backups (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        filename TEXT NOT NULL,
                        file_path TEXT NOT NULL,
                        file_size INTEGER NOT NULL,
                        backup_type TEXT NOT NULL CHECK(backup_type IN ('manual', 'auto', 'scheduled')),
                        compression BOOLEAN DEFAULT 1,
                        encryption BOOLEAN DEFAULT 0,
                        created_by INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE SET NULL
                    )
                `, (err) => {
                    if (err) {
                        reject(err);
                    } else {
                        console.log('✅ تم إنشاء جداول SQLite بنجاح');

                        // ترقية قاعدة البيانات إذا لزم الأمر
                        this.upgradeDatabase().then(() => {
                            resolve();
                        }).catch((error) => {
                            console.error('❌ خطأ في ترقية قاعدة البيانات:', error);
                            resolve(); // نكمل حتى لو فشلت الترقية
                        });
                    }
                });
            });
        });
    }

    // إنشاء جداول PostgreSQL
    async createPostgreSQLTables() {
        try {
            const client = await this.client.connect();

            try {
                // جدول المستخدمين
                await client.query(`
                    CREATE TABLE IF NOT EXISTS users (
                        id SERIAL PRIMARY KEY,
                        telegram_id BIGINT UNIQUE NOT NULL,
                        username VARCHAR(255),
                        first_name VARCHAR(255),
                        last_name VARCHAR(255),
                        language_code VARCHAR(10) DEFAULT 'ar',
                        is_admin BOOLEAN DEFAULT FALSE,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                `);

                // جدول البوتات
                await client.query(`
                    CREATE TABLE IF NOT EXISTS bots (
                        id SERIAL PRIMARY KEY,
                        user_id BIGINT NOT NULL,
                        bot_name VARCHAR(255) NOT NULL,
                        server_host VARCHAR(255) NOT NULL,
                        server_port INTEGER NOT NULL,
                        minecraft_version VARCHAR(50) NOT NULL,
                        edition VARCHAR(20) NOT NULL CHECK(edition IN ('java', 'bedrock')),
                        status VARCHAR(20) DEFAULT 'stopped' CHECK(status IN ('running', 'stopped', 'connecting', 'error', 'created')),
                        auto_reconnect BOOLEAN DEFAULT TRUE,
                        auto_respawn BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_connected TIMESTAMP,
                        connection_count INTEGER DEFAULT 0,
                        FOREIGN KEY (user_id) REFERENCES users (telegram_id) ON DELETE CASCADE,
                        UNIQUE(user_id, bot_name, server_host, server_port)
                    )
                `);

                // جدول إحصائيات البوتات
                await client.query(`
                    CREATE TABLE IF NOT EXISTS bot_stats (
                        id SERIAL PRIMARY KEY,
                        bot_id INTEGER NOT NULL,
                        connected_at TIMESTAMP NOT NULL,
                        disconnected_at TIMESTAMP,
                        duration BIGINT,
                        disconnect_reason TEXT,
                        messages_sent INTEGER DEFAULT 0,
                        commands_executed INTEGER DEFAULT 0,
                        errors_count INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (bot_id) REFERENCES bots (id) ON DELETE CASCADE
                    )
                `);

                // جدول إعدادات النظام
                await client.query(`
                    CREATE TABLE IF NOT EXISTS settings (
                        id SERIAL PRIMARY KEY,
                        key VARCHAR(255) UNIQUE NOT NULL,
                        value TEXT NOT NULL,
                        value_type VARCHAR(20) DEFAULT 'string' CHECK(value_type IN ('string', 'number', 'boolean', 'json')),
                        category VARCHAR(100) DEFAULT 'general',
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                `);

                // جدول النسخ الاحتياطي
                await client.query(`
                    CREATE TABLE IF NOT EXISTS backups (
                        id SERIAL PRIMARY KEY,
                        filename VARCHAR(255) NOT NULL,
                        file_path TEXT NOT NULL,
                        file_size BIGINT NOT NULL,
                        backup_type VARCHAR(20) NOT NULL CHECK(backup_type IN ('manual', 'auto', 'scheduled')),
                        compression BOOLEAN DEFAULT TRUE,
                        encryption BOOLEAN DEFAULT FALSE,
                        created_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE SET NULL
                    )
                `);

                // إنشاء الفهارس
                await client.query('CREATE INDEX IF NOT EXISTS idx_users_telegram_id ON users(telegram_id)');
                await client.query('CREATE INDEX IF NOT EXISTS idx_bots_user_id ON bots(user_id)');
                await client.query('CREATE INDEX IF NOT EXISTS idx_bots_status ON bots(status)');
                await client.query('CREATE INDEX IF NOT EXISTS idx_bot_stats_bot_id ON bot_stats(bot_id)');
                await client.query('CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(key)');
                await client.query('CREATE INDEX IF NOT EXISTS idx_settings_category ON settings(category)');

                console.log('✅ تم إنشاء جداول PostgreSQL بنجاح');

            } finally {
                client.release();
            }
        } catch (error) {
            console.error('❌ خطأ في إنشاء جداول PostgreSQL:', error);
            throw error;
        }
    }

    // ترقية قاعدة البيانات
    async upgradeDatabase() {
        return new Promise((resolve, reject) => {
            try {
                console.log('🔄 فحص الحاجة لترقية قاعدة البيانات...');

                // فحص إذا كان القيد الجديد موجود بالفعل
                this.db.get(`
                    SELECT sql FROM sqlite_master
                    WHERE type='table' AND name='bots'
                `, (err, row) => {
                    if (err) {
                        console.error('خطأ في فحص جدول البوتات:', err);
                        resolve();
                        return;
                    }

                    if (!row || !row.sql) {
                        console.log('جدول البوتات غير موجود، سيتم إنشاؤه تلقائياً');
                        resolve();
                        return;
                    }

                    // فحص إذا كان القيد الجديد موجود
                    if (row.sql.includes('UNIQUE(user_id, bot_name, server_host, server_port)')) {
                        console.log('✅ قاعدة البيانات محدثة بالفعل');
                        resolve();
                        return;
                    }

                    console.log('🔄 ترقية قاعدة البيانات...');

                    // إنشاء جدول جديد بالقيد المحدث
                    this.db.run(`
                        CREATE TABLE bots_new (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            user_id INTEGER NOT NULL,
                            bot_name TEXT NOT NULL,
                            server_host TEXT NOT NULL,
                            server_port INTEGER NOT NULL,
                            minecraft_version TEXT NOT NULL,
                            edition TEXT NOT NULL CHECK(edition IN ('java', 'bedrock')),
                            status TEXT DEFAULT 'stopped' CHECK(status IN ('running', 'stopped', 'connecting', 'error', 'created')),
                            auto_reconnect BOOLEAN DEFAULT 1,
                            auto_respawn BOOLEAN DEFAULT 1,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            last_connected DATETIME,
                            connection_count INTEGER DEFAULT 0,
                            FOREIGN KEY (user_id) REFERENCES users (telegram_id) ON DELETE CASCADE,
                            UNIQUE(user_id, bot_name, server_host, server_port)
                        )
                    `, (err) => {
                        if (err) {
                            console.error('خطأ في إنشاء الجدول الجديد:', err);
                            resolve();
                            return;
                        }

                        // نسخ البيانات من الجدول القديم
                        this.db.run(`
                            INSERT INTO bots_new
                            SELECT * FROM bots
                        `, (err) => {
                            if (err) {
                                console.error('خطأ في نسخ البيانات:', err);
                            }

                            // حذف الجدول القديم
                            this.db.run(`DROP TABLE bots`, (err) => {
                                if (err) {
                                    console.error('خطأ في حذف الجدول القديم:', err);
                                }

                                // إعادة تسمية الجدول الجديد
                                this.db.run(`ALTER TABLE bots_new RENAME TO bots`, (err) => {
                                    if (err) {
                                        console.error('خطأ في إعادة تسمية الجدول:', err);
                                    } else {
                                        console.log('✅ تم ترقية قاعدة البيانات بنجاح');
                                    }
                                    resolve();
                                });
                            });
                        });
                    });
                });
            } catch (error) {
                console.error('❌ خطأ في ترقية قاعدة البيانات:', error);
                resolve();
            }
        });
    }

    // إدراج البيانات الافتراضية
    async insertDefaultData() {
        console.log('📝 إدراج البيانات الافتراضية...');
        
        try {
            // إعدادات افتراضية
            const defaultSettings = [
                { key: 'max_bots_per_user', value: '5', valueType: 'number', description: 'الحد الأقصى لعدد البوتات لكل مستخدم', category: 'limits' },
                { key: 'system_version', value: '2.0.0', valueType: 'string', description: 'إصدار النظام', category: 'system' },
                { key: 'maintenance_mode', value: 'false', valueType: 'boolean', description: 'وضع الصيانة', category: 'system' },
                { key: 'registration_enabled', value: 'true', valueType: 'boolean', description: 'تفعيل التسجيل الجديد', category: 'system' },
                { key: 'supported_java_versions', value: '1.21.4,1.21.3,1.21.1,1.21.0,1.20.6', valueType: 'string', description: 'إصدارات Java المدعومة', category: 'versions' },
                { key: 'supported_bedrock_versions', value: '1.21.93,1.21.92,1.21.90,1.21.80,1.20.30', valueType: 'string', description: 'إصدارات Bedrock المدعومة', category: 'versions' }
            ];

            for (const setting of defaultSettings) {
                await this.insertSettingIfNotExists(setting.key, setting.value, setting.description, setting.valueType, setting.category);
            }

            console.log('✅ تم إدراج البيانات الافتراضية بنجاح');
        } catch (error) {
            console.error('❌ خطأ في إدراج البيانات الافتراضية:', error);
        }
    }

    // إدراج إعداد إذا لم يكن موجوداً
    async insertSettingIfNotExists(key, value, description = null, valueType = 'string', category = 'general') {
        return new Promise((resolve, reject) => {
            this.db.get('SELECT id FROM settings WHERE key = ?', [key], (err, row) => {
                if (err) {
                    reject(err);
                    return;
                }

                if (!row) {
                    this.db.run(
                        'INSERT INTO settings (key, value, value_type, description, category) VALUES (?, ?, ?, ?, ?)',
                        [key, value, valueType, description, category],
                        function(err) {
                            if (err) reject(err);
                            else resolve(this.lastID);
                        }
                    );
                } else {
                    resolve(row.id);
                }
            });
        });
    }

    // ==========================================
    // دوال إدارة المستخدمين
    // ==========================================

    // إنشاء مستخدم جديد
    async createUser(telegramId, userData = {}) {
        const { username, first_name, last_name, language_code } = userData;

        if (this.dbType === 'postgresql') {
            const client = await this.client.connect();
            try {
                const result = await client.query(
                    `INSERT INTO users (telegram_id, username, first_name, last_name, language_code)
                     VALUES ($1, $2, $3, $4, $5) RETURNING id`,
                    [telegramId, username, first_name, last_name, language_code || 'ar']
                );
                return result.rows[0].id;
            } finally {
                client.release();
            }
        } else {
            // SQLite
            return new Promise((resolve, reject) => {
                this.db.run(
                    `INSERT INTO users (telegram_id, username, first_name, last_name, language_code)
                     VALUES (?, ?, ?, ?, ?)`,
                    [telegramId, username, first_name, last_name, language_code || 'ar'],
                    function(err) {
                        if (err) reject(err);
                        else resolve(this.lastID);
                    }
                );
            });
        }
    }

    // الحصول على مستخدم بواسطة معرف التلغرام
    async getUser(telegramId) {
        if (this.dbType === 'postgresql') {
            const client = await this.client.connect();
            try {
                const result = await client.query(
                    'SELECT * FROM users WHERE telegram_id = $1',
                    [telegramId]
                );
                return result.rows[0] || null;
            } finally {
                client.release();
            }
        } else {
            // SQLite
            return new Promise((resolve, reject) => {
                this.db.get(
                    'SELECT * FROM users WHERE telegram_id = ?',
                    [telegramId],
                    (err, row) => {
                        if (err) reject(err);
                        else resolve(row);
                    }
                );
            });
        }
    }

    // تحديث بيانات المستخدم
    async updateUser(telegramId, updates) {
        const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
        const values = Object.values(updates);
        values.push(telegramId);

        return new Promise((resolve, reject) => {
            this.db.run(
                `UPDATE users SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE telegram_id = ?`,
                values,
                function(err) {
                    if (err) reject(err);
                    else resolve(this.changes);
                }
            );
        });
    }

    // تعيين صلاحية الأدمن
    async setAdmin(telegramId, isAdmin = true) {
        return this.updateUser(telegramId, { is_admin: isAdmin ? 1 : 0 });
    }

    // الحصول على جميع الأدمن
    async getAdmins() {
        return new Promise((resolve, reject) => {
            this.db.all(
                'SELECT telegram_id FROM users WHERE is_admin = 1',
                [],
                (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows.map(row => parseInt(row.telegram_id)));
                }
            );
        });
    }

    // ==========================================
    // دوال إدارة البوتات
    // ==========================================

    // إنشاء بوت جديد
    async createBot(userId, botData) {
        const {
            botName,
            serverHost,
            serverPort,
            minecraftVersion,
            edition,
            status = 'created'
        } = botData;

        if (this.dbType === 'postgresql') {
            const client = await this.client.connect();
            try {
                const result = await client.query(
                    `INSERT INTO bots (user_id, bot_name, server_host, server_port, minecraft_version, edition, status, created_at)
                     VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP) RETURNING id`,
                    [userId, botName, serverHost, serverPort, minecraftVersion, edition, status]
                );
                return result.rows[0].id;
            } finally {
                client.release();
            }
        } else {
            // SQLite
            return new Promise((resolve, reject) => {
                this.db.run(
                    `INSERT INTO bots (user_id, bot_name, server_host, server_port, minecraft_version, edition, status, created_at)
                     VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))`,
                    [userId, botName, serverHost, serverPort, minecraftVersion, edition, status],
                    function(err) {
                        if (err) reject(err);
                        else resolve(this.lastID);
                    }
                );
            });
        }
    }

    // فحص وجود بوت بنفس الاسم في نفس السيرفر
    async checkBotExists(botName, serverHost, serverPort) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT id FROM bots WHERE bot_name = ? AND server_host = ? AND server_port = ?',
                [botName, serverHost, serverPort],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });
    }

    // الحصول على بوت بواسطة المعرف
    async getBot(botId) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM bots WHERE id = ?',
                [botId],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });
    }

    // الحصول على بوت بالاسم (لجميع المستخدمين - للاستخدام العام)
    async getBotByName(botName) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM bots WHERE bot_name = ?',
                [botName],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });
    }

    // الحصول على بوت بالاسم للمستخدم المحدد فقط
    async getBotByNameForUser(userId, botName) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM bots WHERE user_id = ? AND bot_name = ?',
                [userId, botName],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });
    }

    // الحصول على بوت بالسيرفر (لجميع المستخدمين - للاستخدام العام)
    async getBotByServer(serverHost, serverPort) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM bots WHERE server_host = ? AND server_port = ?',
                [serverHost, serverPort],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });
    }

    // الحصول على بوت بالسيرفر للمستخدم المحدد فقط
    async getBotByServerForUser(userId, serverHost, serverPort) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM bots WHERE user_id = ? AND server_host = ? AND server_port = ?',
                [userId, serverHost, serverPort],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });
    }

    // التحقق من وجود سيرفر لمستخدمين آخرين (غير المستخدم المحدد)
    async getBotByServerForOtherUsers(excludeUserId, serverHost, serverPort) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM bots WHERE user_id != ? AND server_host = ? AND server_port = ?',
                [excludeUserId, serverHost, serverPort],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });
    }

    // الحصول على إجمالي عدد المستخدمين
    async getTotalUsers() {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT COUNT(*) as count FROM users',
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row ? row.count : 0);
                }
            );
        });
    }

    // الحصول على إجمالي عدد البوتات
    async getTotalBots() {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT COUNT(*) as count FROM bots',
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row ? row.count : 0);
                }
            );
        });
    }

    // الحصول على بوتات المستخدم
    async getUserBots(userId) {
        return new Promise((resolve, reject) => {
            this.db.all(
                'SELECT * FROM bots WHERE user_id = ? ORDER BY created_at DESC',
                [userId],
                (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows || []);
                }
            );
        });
    }

    // تحديث حالة البوت
    async updateBotStatus(botId, status, additionalData = {}) {
        const updates = { status, updated_at: new Date().toISOString(), ...additionalData };
        const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
        const values = Object.values(updates);
        values.push(botId);

        return new Promise((resolve, reject) => {
            this.db.run(
                `UPDATE bots SET ${fields} WHERE id = ?`,
                values,
                function(err) {
                    if (err) reject(err);
                    else resolve(this.changes);
                }
            );
        });
    }

    // حذف بوت
    async deleteBot(botId) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'DELETE FROM bots WHERE id = ?',
                [botId],
                function(err) {
                    if (err) reject(err);
                    else resolve(this.changes);
                }
            );
        });
    }

    // ==========================================
    // دوال الإحصائيات
    // ==========================================

    // إضافة إحصائية بوت
    async addBotStat(botId, connectedAt, disconnectedAt = null, additionalData = {}) {
        return new Promise((resolve, reject) => {
            const duration = disconnectedAt ?
                new Date(disconnectedAt) - new Date(connectedAt) : null;

            const { disconnect_reason, messages_sent, commands_executed, errors_count } = additionalData;

            this.db.run(
                `INSERT INTO bot_stats (bot_id, connected_at, disconnected_at, duration,
                 disconnect_reason, messages_sent, commands_executed, errors_count)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [botId, connectedAt, disconnectedAt, duration, disconnect_reason,
                 messages_sent || 0, commands_executed || 0, errors_count || 0],
                function(err) {
                    if (err) reject(err);
                    else resolve(this.lastID);
                }
            );
        });
    }

    // الحصول على إحصائيات بوت
    async getBotStats(botId, limit = 10) {
        return new Promise((resolve, reject) => {
            this.db.all(
                'SELECT * FROM bot_stats WHERE bot_id = ? ORDER BY created_at DESC LIMIT ?',
                [botId, limit],
                (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows || []);
                }
            );
        });
    }

    // الحصول على بوت بواسطة المستخدم والتفاصيل
    async getBotByUserAndDetails(userId, botName, serverHost, serverPort) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM bots WHERE user_id = ? AND bot_name = ? AND server_host = ? AND server_port = ?',
                [userId, botName, serverHost, serverPort],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row || null);
                }
            );
        });
    }

    // ==========================================
    // دوال الإعدادات
    // ==========================================

    // الحصول على إعداد
    async getSetting(key) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT value FROM settings WHERE key = ?',
                [key],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row ? row.value : null);
                }
            );
        });
    }

    // تحديث إعداد
    async setSetting(key, value, description = null) {
        return new Promise((resolve, reject) => {
            this.db.run(
                `INSERT OR REPLACE INTO settings (key, value, description, updated_at)
                 VALUES (?, ?, ?, CURRENT_TIMESTAMP)`,
                [key, value, description],
                function(err) {
                    if (err) reject(err);
                    else resolve(this.changes);
                }
            );
        });
    }

    // الحصول على جميع البوتات
    async getAllBots() {
        return new Promise((resolve, reject) => {
            this.db.all(
                'SELECT * FROM bots ORDER BY created_at DESC',
                [],
                (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows || []);
                }
            );
        });
    }

    // الحصول على إحصائيات قاعدة البيانات
    async getDatabaseStats() {
        try {
            const stats = {
                type: 'SQLite',
                tables: 0,
                size: 'غير محدد'
            };

            // عدد الجداول
            const tables = await new Promise((resolve, reject) => {
                this.db.all(`
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                `, (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                });
            });
            stats.tables = tables.length;

            // حجم قاعدة البيانات (تقريبي)
            const fs = require('fs');
            try {
                const dbPath = './minecraft_bot.db';
                if (fs.existsSync(dbPath)) {
                    const fileStats = fs.statSync(dbPath);
                    stats.size = `${Math.round(fileStats.size / 1024)} KB`;
                }
            } catch (error) {
                console.log('تعذر الحصول على حجم قاعدة البيانات');
            }

            return stats;
        } catch (error) {
            console.error('❌ خطأ في الحصول على إحصائيات قاعدة البيانات:', error);
            return {
                type: 'SQLite',
                tables: 'غير محدد',
                size: 'غير محدد'
            };
        }
    }

    // ==========================================
    // دوال إدارة الإعدادات
    // ==========================================

    // الحصول على إعداد
    async getSetting(key, defaultValue = null) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT value, value_type FROM settings WHERE key = ?',
                [key],
                (err, row) => {
                    if (err) {
                        reject(err);
                    } else if (row) {
                        // تحويل القيمة حسب النوع
                        let value = row.value;
                        switch (row.value_type) {
                            case 'number':
                                value = parseFloat(value);
                                break;
                            case 'boolean':
                                value = value === 'true';
                                break;
                            case 'json':
                                try {
                                    value = JSON.parse(value);
                                } catch (e) {
                                    console.error(`خطأ في تحليل JSON للإعداد ${key}:`, e);
                                    value = defaultValue;
                                }
                                break;
                        }
                        resolve(value);
                    } else {
                        resolve(defaultValue);
                    }
                }
            );
        });
    }

    // تعيين إعداد
    async setSetting(key, value, valueType = 'string', description = null, category = 'general') {
        return new Promise((resolve, reject) => {
            // تحويل القيمة إلى نص للحفظ
            let stringValue = value;
            if (valueType === 'json') {
                stringValue = JSON.stringify(value);
            } else if (valueType === 'boolean') {
                stringValue = value ? 'true' : 'false';
            } else {
                stringValue = String(value);
            }

            this.db.run(
                `INSERT OR REPLACE INTO settings (key, value, value_type, description, category, updated_at)
                 VALUES (?, ?, ?, ?, ?, datetime('now'))`,
                [key, stringValue, valueType, description, category],
                function(err) {
                    if (err) reject(err);
                    else resolve(this.lastID);
                }
            );
        });
    }

    // حذف إعداد
    async deleteSetting(key) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'DELETE FROM settings WHERE key = ?',
                [key],
                function(err) {
                    if (err) reject(err);
                    else resolve(this.changes > 0);
                }
            );
        });
    }

    // الحصول على جميع الإعدادات
    async getAllSettings(category = null) {
        return new Promise((resolve, reject) => {
            let query = 'SELECT * FROM settings';
            let params = [];

            if (category) {
                query += ' WHERE category = ?';
                params.push(category);
            }

            query += ' ORDER BY category, key';

            this.db.all(query, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    // تحويل القيم حسب النوع
                    const settings = {};
                    rows.forEach(row => {
                        let value = row.value;
                        switch (row.value_type) {
                            case 'number':
                                value = parseFloat(value);
                                break;
                            case 'boolean':
                                value = value === 'true';
                                break;
                            case 'json':
                                try {
                                    value = JSON.parse(value);
                                } catch (e) {
                                    console.error(`خطأ في تحليل JSON للإعداد ${row.key}:`, e);
                                }
                                break;
                        }
                        settings[row.key] = {
                            value: value,
                            type: row.value_type,
                            description: row.description,
                            category: row.category,
                            updated_at: row.updated_at
                        };
                    });
                    resolve(settings);
                }
            });
        });
    }

    // إغلاق الاتصال
    async close() {
        try {
            if (this.db && this.dbType === 'sqlite') {
                this.db.close();
            } else if (this.client) {
                await this.client.end();
            }
            console.log('✅ تم إغلاق اتصال قاعدة البيانات');
        } catch (error) {
            console.error('❌ خطأ في إغلاق قاعدة البيانات:', error);
        }
    }
}

module.exports = DatabaseManager;
